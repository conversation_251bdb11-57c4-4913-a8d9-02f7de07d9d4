<!DOCTYPE html>
<html lang="en">
  <head>
    <title>BasicSwapDEX.com</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="css/tailwind.min.css">
    <link rel=icon sizes="32x32" type="image/png" href="images/favicon-32.png">
    <meta property="og:url" content="https://basicswapdex.com">
    <meta property="og:site_name" content="BasicSwapDEX">
    <meta property="og:title" content="BasicSwapDEX">
    <meta property="og:description" content="The World's Most Secure and Decentralized DEX.Safely swap cryptocurrencies without central points of failure. It’s free, completely trustless, and highly secure.">
    <meta property="og:image" content="/site-meta.png">
    <script src="js/main.js"></script>
    <style>
      .blur {
        -webkit-filter: blur(5px);
        -moz-filter: blur(5px);
        -o-filter: blur(5px);
        -ms-filter: blur(5px);
        filter: blur(5px);
      }
    </style>  
  </head>
  <body class="antialiased bg-body text-body font-body">
    <section>
      <nav class="relative bg-gray-700">
        <div class="p-8 container flex flex-wrap items-center justify-between items-center mx-auto">
          <div class="flex justify-between items-center xl:w-full">
            <div class="xl:w-1/3">
              <a class="block max-w-max xl:mr-14" href="/">
                <img class="h-12" src="images/basicswap-logo.svg" alt="">
              </a>
            </div>

            <div class="hidden xl:block">
              <ul class="flex justify-center">
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="/">Home</a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="faq">FAQ</a>
                </li>
                <li class="mr-12">
                  <a class="text-white hover:text-coolGray-50 font-medium" href="about">About</a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="donations">Donations</a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" target=”_blank” href="https://docs.basicswapdex.com/docs/intro">Docs</a>
                </li> 
                <li>
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" target="_blank" href="https://particl.news/tag/basicswap">Blog</a>
                </li>
              </ul>
            </div>

            <div class="hidden xl:block xl:w-1/3">
              <div class="flex items-center justify-end">
                  <a class="flex flex-wrap justify-center inline-block px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-bold text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none" href="https://github.com/basicswap/" target="_blank" rel="noopener noreferrer">
                  <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" class="mr-2" viewBox="0 0 24 24"><g fill="#ffffff"><path fill-rule="evenodd" clip-rule="evenodd" fill="#ffffff" d="M12,0.3c-6.6,0-12,5.4-12,12c0,5.3,3.4,9.8,8.2,11.4 C8.8,23.8,9,23.4,9,23.1c0-0.3,0-1,0-2c-3.3,0.7-4-1.6-4-1.6c-0.5-1.4-1.3-1.8-1.3-1.8C2.5,17,3.7,17,3.7,17 c1.2,0.1,1.8,1.2,1.8,1.2c1.1,1.8,2.8,1.3,3.5,1c0.1-0.8,0.4-1.3,0.8-1.6c-2.7-0.3-5.5-1.3-5.5-5.9c0-1.3,0.5-2.4,1.2-3.2 C5.5,8.1,5,6.9,5.7,5.3c0,0,1-0.3,3.3,1.2c1-0.3,2-0.4,3-0.4c1,0,2,0.1,3,0.4c2.3-1.6,3.3-1.2,3.3-1.2c0.7,1.7,0.2,2.9,0.1,3.2 c0.8,0.8,1.2,1.9,1.2,3.2c0,4.6-2.8,5.6-5.5,5.9c0.4,0.4,0.8,1.1,0.8,2.2c0,1.6,0,2.9,0,3.3c0,0.3,0.2,0.7,0.8,0.6 c4.8-1.6,8.2-6.1,8.2-11.4C24,5.7,18.6,0.3,12,0.3z"></path></g></svg> <span>BasicSwap Github</span></a>
                  </div>
              </div>
          </div>
          <button class="navbar-burger self-center xl:hidden">
            <svg width="35" height="35" viewbox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect class="text-coolGray-800" width="32" height="32" rx="6" fill="currentColor"></rect>
              <path class="text-coolGray-400" d="M7 12H25C25.2652 12 25.5196 11.8946 25.7071 11.7071C25.8946 11.5196 26 11.2652 26 11C26 10.7348 25.8946 10.4804 25.7071 10.2929C25.5196 10.1054 25.2652 10 25 10H7C6.73478 10 6.48043 10.1054 6.29289 10.2929C6.10536 10.4804 6 10.7348 6 11C6 11.2652 6.10536 11.5196 6.29289 11.7071C6.48043 11.8946 6.73478 12 7 12ZM25 15H7C6.73478 15 6.48043 15.1054 6.29289 15.2929C6.10536 15.4804 6 15.7348 6 16C6 16.2652 6.10536 16.5196 6.29289 16.7071C6.48043 16.8946 6.73478 17 7 17H25C25.2652 17 25.5196 16.8946 25.7071 16.7071C25.8946 16.5196 26 16.2652 26 16C26 15.7348 25.8946 15.4804 25.7071 15.2929C25.5196 15.1054 25.2652 15 25 15ZM25 20H7C6.73478 20 6.48043 20.1054 6.29289 20.2929C6.10536 20.4804 6 20.7348 6 21C6 21.2652 6.10536 21.5196 6.29289 21.7071C6.48043 21.8946 6.73478 22 7 22H25C25.2652 22 25.5196 21.8946 25.7071 21.7071C25.8946 21.5196 26 21.2652 26 21C26 20.7348 25.8946 20.4804 25.7071 20.2929C25.5196 20.1054 25.2652 20 25 20Z" fill="currentColor"></path>
            </svg>
          </button>
        </div>
      </nav>
      <div class="navbar-menu hidden fixed top-0 left-0 z-50 w-full h-full bg-coolGray-900 bg-opacity-50">
        <div class="fixed top-0 left-0 bottom-0 w-full w-4/6 max-w-xs bg-coolGray-900">
          <nav class="relative p-6 h-full overflow-y-auto">
            <div class="flex flex-col justify-between h-full">
              <a class="inline-block" href="#">
                <img class="h-8" src="images/basicswap-logo.svg" alt="">
              </a>
              <ul class="py-6">
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="/">Home</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="markets">Markets</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="/faq">FAQ</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="/about">About</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="/roadmap">Roadmap</a>
                </li>                
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" target=”_blank” href="https://particl.news/tag/basicswap">Blog</a>
                </li>
              </ul>
              <div class="flex flex-wrap">
                <div class="w-full">
                  <a class="inline-block py-4 px-5 w-full text-sm leading-5 text-white bg-blue-500 hover:bg-blue-600 font-medium text-center focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="https://github.com/basicswap/basicswap" target="_blank" rel="noopener noreferrer">BasicSwap Github</a>
                </div>
              </div>
            </div>
          </nav>
          <a class="navbar-close absolute top-5 p-4 right-3" href="#">
            <svg width="12" height="12" viewbox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.94004 5.99988L11.14 1.80655C11.2656 1.68101 11.3361 1.51075 11.3361 1.33321C11.3361 1.15568 11.2656 0.985415 11.14 0.859879C11.0145 0.734344 10.8442 0.663818 10.6667 0.663818C10.4892 0.663818 10.3189 0.734344 10.1934 0.859879L6.00004 5.05988L1.80671 0.859879C1.68117 0.734344 1.51091 0.663819 1.33337 0.663819C1.15584 0.663819 0.985576 0.734344 0.860041 0.859879C0.734505 0.985415 0.66398 1.15568 0.66398 1.33321C0.66398 1.51075 0.734505 1.68101 0.860041 1.80655L5.06004 5.99988L0.860041 10.1932C0.797555 10.2552 0.747959 10.3289 0.714113 10.4102C0.680267 10.4914 0.662842 10.5785 0.662842 10.6665C0.662842 10.7546 0.680267 10.8417 0.714113 10.9229C0.747959 11.0042 0.797555 11.0779 0.860041 11.1399C0.922016 11.2024 0.99575 11.252 1.07699 11.2858C1.15823 11.3197 1.24537 11.3371 1.33337 11.3371C1.42138 11.3371 1.50852 11.3197 1.58976 11.2858C1.671 11.252 1.74473 11.2024 1.80671 11.1399L6.00004 6.93988L10.1934 11.1399C10.2554 11.2024 10.3291 11.252 10.4103 11.2858C10.4916 11.3197 10.5787 11.3371 10.6667 11.3371C10.7547 11.3371 10.8419 11.3197 10.9231 11.2858C11.0043 11.252 11.0781 11.2024 11.14 11.1399C11.2025 11.0779 11.2521 11.0042 11.286 10.9229C11.3198 10.8417 11.3372 10.7546 11.3372 10.6665C11.3372 10.5785 11.3198 10.4914 11.286 10.4102C11.2521 10.3289 11.2025 10.2552 11.14 10.1932L6.94004 5.99988Z" fill="#8896AB"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <section class="py-32 bg-white relative overflow-hidden">
      <div class="container px-4 mx-auto">
        <div class="flex flex-wrap items-center -m-8">
          <div class="w-full md:w-1/2 p-8">
            <h2 class="mb-9 text-6xl md:text-6xl xl:text-10xl font-bold font-heading tracking-px-n leading-none">About BasicSwap</h2>
            <p class="mb-10 text-lg md:text-xl text-coolGray-500 font-medium">BasicSwap is the world's most secure and decentralized cross-chain trading exchange (DEX) with no central point of failure that lets you swap cryptocurrencies such as Bitcoin or Monero without fees, friction, or the third-party involvement.<br><br>BasicSwap is crafted to uphold the highest standards of freedom in direct opposition to the growing threats to our sovereignty in the digital age. 
            </p>
            <div class="mb-11 md:inline-block rounded-xl shadow-4xl"></div>
            <div class="flex flex-wrap -m-2">
              <div class="w-auto p-2">
                <img src="images/bottle.png" alt="">
              </div>
              <div class="flex-1 p-4">
                <p class="text-gray-600 font-medium text-lg md:text-xl"><b>The genie is now out of the bottle.</b></p>
              </div>
            </div>
          </div>
          <div class="w-full md:w-1/2 p-8">
            <img class="" src="images/bs.png" alt="">
          </div>
        </div>
      </div>
    </section>
    <section class="py-36 bg-gray-50 overflow-hidden">
      <div class="container mx-auto px-4">
        <div class="flex flex-wrap items-center -m-6">
          <div class="flex w-full md:w-1/2 p-6">
            <img class="mx-auto relative top-10 mx-auto transform hover:-translate-y-16 transition ease-in-out duration-500" src="images/dev.png" alt="">
          </div>
          <div class="relative w-full md:w-1/2 p-6">
            <div class="relative z-10 max-w-2xl text-center">
              <h2 class="mb-8 text-4xl md:text-5xl leading-tight text-coolGray-900 font-bold tracking-tighter">The Open-Source Revolution</h2>
              <p class="mb-8 text-gray-600 text-lg">Meeting today’s challenges requires an open-source and uncompromisingly decentralized revolution. That’s why BasicSwap’s code is entirely open and available for all to inspect. Don’t trust, verify!</p>
              <p class="mb-8 text-gray-600 text-lg bold">
                <b>Check out the source code on Github and become a contributor!</b>
              </p>
              <div class="flex flex-wrap justify-center">
                <div class="w-full md:w-auto p-1.5 ml-2">
                  <a class="w-full inline-block py-3 px-4 text-sm leading-5 text-blue-50 bg-blue-500 hover:bg-blue-600 font-medium focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="https://github.com/basicswap/basicswap" target="_blank" rel="noopener noreferrer">Check out the code</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <section class="bg-white overflow-hidden">
      <div class="container px-4 mx-auto">
        <div class="flex flex-wrap lg:items-center pt-24 pb-12 -mx-4">
          <div class="w-full md:w-1/4 lg:w-auto px-4">
            <a class="block mb-5 md:mb-0 max-w-max" href="/">
              <img class="h-8" src="images/basicswap-logo-dark.svg" alt="">
            </a>
          </div>
          <div class="w-full md:w-3/4 lg:flex-1 px-4">
            <div class="flex flex-wrap justify-end -mx-3 lg:-mx-6">
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-w hover:text-coolGray-600 font-medium" href="/">Home</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="faq">FAQ</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="about">About</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" target=”_blank” href="https://docs.basicswapdex.com/docs/intro">Docs</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-600 hover:text-coolGray-700 font-medium" href="donations">Donations</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" target="_blank" href="https://particl.news/tag/basicswap">Blog</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="terms">Terms and Conditions</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="/mediakit/mediakit-basicswap.zip">Mediakit</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="border-b border-coolGray-100"></div>
      <div class="container px-4 mx-auto mb-10">
        <div class="flex flex-wrap items-center pt-12">
          <div class="w-full md:w-1/2 mb-6 md:mb-0">
            <div class="flex items-center">
              <p class="mr-1 text-sm text-gray-900 font-medium">© 2025~ </p>
              <p class="text-sm text-coolGray-400 font-medium">BasicSwapDEX</p>
            </div>
          </div>
          <div class="w-full md:w-1/2">
            <div class="flex flex-wrap md:justify-end -mx-5">
              <div class="px-5">
                <a class="inline-block text-coolGray-300 hover:text-coolGray-400" href="https://github.com/basicswap/basicswap" target="_blank" rel="noopener noreferrer">
                  <svg width="18" height="18" viewbox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 0C4.0275 0 0 4.13211 0 9.22838C0 13.3065 2.5785 16.7648 6.15375 17.9841C6.60375 18.0709 6.76875 17.7853 6.76875 17.5403C6.76875 17.3212 6.76125 16.7405 6.7575 15.9712C4.254 16.5277 3.726 14.7332 3.726 14.7332C3.3165 13.6681 2.72475 13.3832 2.72475 13.3832C1.9095 12.8111 2.78775 12.8229 2.78775 12.8229C3.6915 12.887 4.16625 13.7737 4.16625 13.7737C4.96875 15.1847 6.273 14.777 6.7875 14.5414C6.8685 13.9443 7.10025 13.5381 7.3575 13.3073C5.35875 13.0764 3.258 12.2829 3.258 8.74709C3.258 7.73988 3.60675 6.91659 4.18425 6.27095C4.083 6.03774 3.77925 5.0994 4.263 3.82846C4.263 3.82846 5.01675 3.58116 6.738 4.77462C7.458 4.56958 8.223 4.46785 8.988 4.46315C9.753 4.46785 10.518 4.56958 11.238 4.77462C12.948 3.58116 13.7017 3.82846 13.7017 3.82846C14.1855 5.0994 13.8818 6.03774 13.7917 6.27095C14.3655 6.91659 14.7142 7.73988 14.7142 8.74709C14.7142 12.2923 12.6105 13.0725 10.608 13.2995C10.923 13.5765 11.2155 14.1423 11.2155 15.0071C11.2155 16.242 11.2043 17.2344 11.2043 17.5341C11.2043 17.7759 11.3617 18.0647 11.823 17.9723C15.4237 16.7609 18 13.3002 18 9.22838C18 4.13211 13.9703 0 9 0Z" fill="currentColor"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
  </body>
</html>
