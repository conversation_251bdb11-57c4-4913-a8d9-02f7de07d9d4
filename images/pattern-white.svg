<svg width="1440" height="992" viewBox="0 0 1440 992" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_628_19126)">
<rect width="1440" height="992" fill="#F7F8F9"/>
<g opacity="0.4">
<rect width="1440" height="1320" fill="white"/>
<line x1="1464" y1="672.5" x2="-24" y2="672.5" stroke="#EEF0F3"/>
<line x1="1464" y1="696.5" x2="-24" y2="696.5" stroke="#EEF0F3"/>
<line x1="1464" y1="720.5" x2="-24" y2="720.5" stroke="#EEF0F3"/>
<line x1="1464" y1="744.5" x2="-24" y2="744.5" stroke="#EEF0F3"/>
<line x1="1464" y1="768.5" x2="-24" y2="768.5" stroke="#EEF0F3"/>
<line x1="1464" y1="792.5" x2="-24" y2="792.5" stroke="#EEF0F3"/>
<line x1="1464" y1="816.5" x2="-24" y2="816.5" stroke="#EEF0F3"/>
<line x1="1464" y1="840.5" x2="-24" y2="840.5" stroke="#EEF0F3"/>
<line x1="1464" y1="864.5" x2="-24" y2="864.5" stroke="#EEF0F3"/>
<line x1="1464" y1="888.5" x2="-24" y2="888.5" stroke="#EEF0F3"/>
<line x1="1464" y1="912.5" x2="-24" y2="912.5" stroke="#EEF0F3"/>
<line x1="1464" y1="936.5" x2="-24" y2="936.5" stroke="#EEF0F3"/>
<line x1="1464" y1="960.5" x2="-24" y2="960.5" stroke="#EEF0F3"/>
<line x1="1464" y1="984.5" x2="-24" y2="984.5" stroke="#EEF0F3"/>
<line x1="24.5" y1="648" x2="24.5" y2="1368" stroke="#EEF0F3"/>
<line x1="24.5" y1="648" x2="24.5" y2="1368" stroke="#EEF0F3"/>
<line x1="48.5" y1="648" x2="48.5" y2="1368" stroke="#EEF0F3"/>
<line x1="72.5" y1="648" x2="72.5" y2="1368" stroke="#EEF0F3"/>
<line x1="96.5" y1="648" x2="96.5" y2="1368" stroke="#EEF0F3"/>
<line x1="120.5" y1="648" x2="120.5" y2="1368" stroke="#EEF0F3"/>
<line x1="144.5" y1="648" x2="144.5" y2="1368" stroke="#EEF0F3"/>
<line x1="168.5" y1="648" x2="168.5" y2="1368" stroke="#EEF0F3"/>
<line x1="192.5" y1="648" x2="192.5" y2="1368" stroke="#EEF0F3"/>
<line x1="216.5" y1="648" x2="216.5" y2="1368" stroke="#EEF0F3"/>
<line x1="240.5" y1="648" x2="240.5" y2="1368" stroke="#EEF0F3"/>
<line x1="264.5" y1="648" x2="264.5" y2="1368" stroke="#EEF0F3"/>
<line x1="288.5" y1="648" x2="288.5" y2="1368" stroke="#EEF0F3"/>
<line x1="312.5" y1="648" x2="312.5" y2="1368" stroke="#EEF0F3"/>
<line x1="336.5" y1="648" x2="336.5" y2="1368" stroke="#EEF0F3"/>
<line x1="360.5" y1="648" x2="360.5" y2="1368" stroke="#EEF0F3"/>
<line x1="384.5" y1="648" x2="384.5" y2="1368" stroke="#EEF0F3"/>
<line x1="408.5" y1="648" x2="408.5" y2="1368" stroke="#EEF0F3"/>
<line x1="432.5" y1="648" x2="432.5" y2="1368" stroke="#EEF0F3"/>
<line x1="456.5" y1="648" x2="456.5" y2="1368" stroke="#EEF0F3"/>
<line x1="480.5" y1="648" x2="480.5" y2="1368" stroke="#EEF0F3"/>
<line x1="504.5" y1="648" x2="504.5" y2="1368" stroke="#EEF0F3"/>
<line x1="528.5" y1="648" x2="528.5" y2="1368" stroke="#EEF0F3"/>
<line x1="552.5" y1="648" x2="552.5" y2="1368" stroke="#EEF0F3"/>
<line x1="576.5" y1="648" x2="576.5" y2="1368" stroke="#EEF0F3"/>
<line x1="600.5" y1="648" x2="600.5" y2="1368" stroke="#EEF0F3"/>
<line x1="624.5" y1="648" x2="624.5" y2="1368" stroke="#EEF0F3"/>
<line x1="648.5" y1="648" x2="648.5" y2="1368" stroke="#EEF0F3"/>
<line x1="672.5" y1="648" x2="672.5" y2="1368" stroke="#EEF0F3"/>
<line x1="696.5" y1="648" x2="696.5" y2="1368" stroke="#EEF0F3"/>
<line x1="720.5" y1="648" x2="720.5" y2="1368" stroke="#EEF0F3"/>
<line x1="744.5" y1="648" x2="744.5" y2="1368" stroke="#EEF0F3"/>
<line x1="768.5" y1="648" x2="768.5" y2="1368" stroke="#EEF0F3"/>
<line x1="792.5" y1="648" x2="792.5" y2="1368" stroke="#EEF0F3"/>
<line x1="816.5" y1="648" x2="816.5" y2="1368" stroke="#EEF0F3"/>
<line x1="840.5" y1="648" x2="840.5" y2="1368" stroke="#EEF0F3"/>
<line x1="864.5" y1="648" x2="864.5" y2="1368" stroke="#EEF0F3"/>
<line x1="888.5" y1="648" x2="888.5" y2="1368" stroke="#EEF0F3"/>
<line x1="912.5" y1="648" x2="912.5" y2="1368" stroke="#EEF0F3"/>
<line x1="936.5" y1="648" x2="936.5" y2="1368" stroke="#EEF0F3"/>
<line x1="960.5" y1="648" x2="960.5" y2="1368" stroke="#EEF0F3"/>
<line x1="984.5" y1="648" x2="984.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1008.5" y1="648" x2="1008.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1032.5" y1="648" x2="1032.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1056.5" y1="648" x2="1056.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1080.5" y1="648" x2="1080.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1104.5" y1="648" x2="1104.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1128.5" y1="648" x2="1128.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1152.5" y1="648" x2="1152.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1176.5" y1="648" x2="1176.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1200.5" y1="648" x2="1200.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1224.5" y1="648" x2="1224.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1248.5" y1="648" x2="1248.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1272.5" y1="648" x2="1272.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1296.5" y1="648" x2="1296.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1320.5" y1="648" x2="1320.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1344.5" y1="648" x2="1344.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1368.5" y1="648" x2="1368.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1392.5" y1="648" x2="1392.5" y2="1368" stroke="#EEF0F3"/>
<line x1="1416.5" y1="648" x2="1416.5" y2="1368" stroke="#EEF0F3"/>
<line x1="0.5" y1="-48" x2="0.499969" y2="672" stroke="#EEF0F3"/>
<line x1="1464" y1="0.5" x2="-24" y2="0.5" stroke="#EEF0F3"/>
<line x1="1464" y1="24.5" x2="-24" y2="24.5" stroke="#EEF0F3"/>
<line x1="1464" y1="48.5" x2="-24" y2="48.5" stroke="#EEF0F3"/>
<line x1="1464" y1="72.5" x2="-24" y2="72.5" stroke="#EEF0F3"/>
<line x1="1464" y1="96.5" x2="-24" y2="96.5" stroke="#EEF0F3"/>
<line x1="1464" y1="120.5" x2="-24" y2="120.5" stroke="#EEF0F3"/>
<line x1="1464" y1="144.5" x2="-24" y2="144.5" stroke="#EEF0F3"/>
<line x1="1464" y1="168.5" x2="-24" y2="168.5" stroke="#EEF0F3"/>
<line x1="1464" y1="192.5" x2="-24" y2="192.5" stroke="#EEF0F3"/>
<line x1="1464" y1="216.5" x2="-24" y2="216.5" stroke="#EEF0F3"/>
<line x1="1464" y1="240.5" x2="-24" y2="240.5" stroke="#EEF0F3"/>
<line x1="1464" y1="264.5" x2="-24" y2="264.5" stroke="#EEF0F3"/>
<line x1="1464" y1="288.5" x2="-24" y2="288.5" stroke="#EEF0F3"/>
<line x1="1464" y1="312.5" x2="-24" y2="312.5" stroke="#EEF0F3"/>
<line x1="1464" y1="336.5" x2="-24" y2="336.5" stroke="#EEF0F3"/>
<line x1="1464" y1="360.5" x2="-24" y2="360.5" stroke="#EEF0F3"/>
<line x1="1464" y1="384.5" x2="-24" y2="384.5" stroke="#EEF0F3"/>
<line x1="1464" y1="408.5" x2="-24" y2="408.5" stroke="#EEF0F3"/>
<line x1="1464" y1="432.5" x2="-24" y2="432.5" stroke="#EEF0F3"/>
<line x1="1464" y1="456.5" x2="-24" y2="456.5" stroke="#EEF0F3"/>
<line x1="1464" y1="480.5" x2="-24" y2="480.5" stroke="#EEF0F3"/>
<line x1="1464" y1="504.5" x2="-24" y2="504.5" stroke="#EEF0F3"/>
<line x1="1464" y1="528.5" x2="-24" y2="528.5" stroke="#EEF0F3"/>
<line x1="1464" y1="552.5" x2="-24" y2="552.5" stroke="#EEF0F3"/>
<line x1="1464" y1="576.5" x2="-24" y2="576.5" stroke="#EEF0F3"/>
<line x1="1464" y1="600.5" x2="-24" y2="600.5" stroke="#EEF0F3"/>
<line x1="1464" y1="624.5" x2="-24" y2="624.5" stroke="#EEF0F3"/>
<line x1="1464" y1="648.5" x2="-24" y2="648.5" stroke="#EEF0F3"/>
<line x1="24.5" y1="-48" x2="24.5" y2="672" stroke="#EEF0F3"/>
<line x1="24.5" y1="-48" x2="24.5" y2="672" stroke="#EEF0F3"/>
<line x1="48.5" y1="-48" x2="48.5" y2="672" stroke="#EEF0F3"/>
<line x1="72.5" y1="-48" x2="72.5" y2="672" stroke="#EEF0F3"/>
<line x1="96.5" y1="-48" x2="96.5" y2="672" stroke="#EEF0F3"/>
<line x1="120.5" y1="-48" x2="120.5" y2="672" stroke="#EEF0F3"/>
<line x1="144.5" y1="-48" x2="144.5" y2="672" stroke="#EEF0F3"/>
<line x1="168.5" y1="-48" x2="168.5" y2="672" stroke="#EEF0F3"/>
<line x1="192.5" y1="-48" x2="192.5" y2="672" stroke="#EEF0F3"/>
<line x1="216.5" y1="-48" x2="216.5" y2="672" stroke="#EEF0F3"/>
<line x1="240.5" y1="-48" x2="240.5" y2="672" stroke="#EEF0F3"/>
<line x1="264.5" y1="-48" x2="264.5" y2="672" stroke="#EEF0F3"/>
<line x1="288.5" y1="-48" x2="288.5" y2="672" stroke="#EEF0F3"/>
<line x1="312.5" y1="-48" x2="312.5" y2="672" stroke="#EEF0F3"/>
<line x1="336.5" y1="-48" x2="336.5" y2="672" stroke="#EEF0F3"/>
<line x1="360.5" y1="-48" x2="360.5" y2="672" stroke="#EEF0F3"/>
<line x1="384.5" y1="-48" x2="384.5" y2="672" stroke="#EEF0F3"/>
<line x1="408.5" y1="-48" x2="408.5" y2="672" stroke="#EEF0F3"/>
<line x1="432.5" y1="-48" x2="432.5" y2="672" stroke="#EEF0F3"/>
<line x1="456.5" y1="-48" x2="456.5" y2="672" stroke="#EEF0F3"/>
<line x1="480.5" y1="-48" x2="480.5" y2="672" stroke="#EEF0F3"/>
<line x1="504.5" y1="-48" x2="504.5" y2="672" stroke="#EEF0F3"/>
<line x1="528.5" y1="-48" x2="528.5" y2="672" stroke="#EEF0F3"/>
<line x1="552.5" y1="-48" x2="552.5" y2="672" stroke="#EEF0F3"/>
<line x1="576.5" y1="-48" x2="576.5" y2="672" stroke="#EEF0F3"/>
<line x1="600.5" y1="-48" x2="600.5" y2="672" stroke="#EEF0F3"/>
<line x1="624.5" y1="-48" x2="624.5" y2="672" stroke="#EEF0F3"/>
<line x1="648.5" y1="-48" x2="648.5" y2="672" stroke="#EEF0F3"/>
<line x1="672.5" y1="-48" x2="672.5" y2="672" stroke="#EEF0F3"/>
<line x1="696.5" y1="-48" x2="696.5" y2="672" stroke="#EEF0F3"/>
<line x1="720.5" y1="-48" x2="720.5" y2="672" stroke="#EEF0F3"/>
<line x1="744.5" y1="-48" x2="744.5" y2="672" stroke="#EEF0F3"/>
<line x1="768.5" y1="-48" x2="768.5" y2="672" stroke="#EEF0F3"/>
<line x1="792.5" y1="-48" x2="792.5" y2="672" stroke="#EEF0F3"/>
<line x1="816.5" y1="-48" x2="816.5" y2="672" stroke="#EEF0F3"/>
<line x1="840.5" y1="-48" x2="840.5" y2="672" stroke="#EEF0F3"/>
<line x1="864.5" y1="-48" x2="864.5" y2="672" stroke="#EEF0F3"/>
<line x1="888.5" y1="-48" x2="888.5" y2="672" stroke="#EEF0F3"/>
<line x1="912.5" y1="-48" x2="912.5" y2="672" stroke="#EEF0F3"/>
<line x1="936.5" y1="-48" x2="936.5" y2="672" stroke="#EEF0F3"/>
<line x1="960.5" y1="-48" x2="960.5" y2="672" stroke="#EEF0F3"/>
<line x1="984.5" y1="-48" x2="984.5" y2="672" stroke="#EEF0F3"/>
<line x1="1008.5" y1="-48" x2="1008.5" y2="672" stroke="#EEF0F3"/>
<line x1="1032.5" y1="-48" x2="1032.5" y2="672" stroke="#EEF0F3"/>
<line x1="1056.5" y1="-48" x2="1056.5" y2="672" stroke="#EEF0F3"/>
<line x1="1080.5" y1="-48" x2="1080.5" y2="672" stroke="#EEF0F3"/>
<line x1="1104.5" y1="-48" x2="1104.5" y2="672" stroke="#EEF0F3"/>
<line x1="1128.5" y1="-48" x2="1128.5" y2="672" stroke="#EEF0F3"/>
<line x1="1152.5" y1="-48" x2="1152.5" y2="672" stroke="#EEF0F3"/>
<line x1="1176.5" y1="-48" x2="1176.5" y2="672" stroke="#EEF0F3"/>
<line x1="1200.5" y1="-48" x2="1200.5" y2="672" stroke="#EEF0F3"/>
<line x1="1224.5" y1="-48" x2="1224.5" y2="672" stroke="#EEF0F3"/>
<line x1="1248.5" y1="-48" x2="1248.5" y2="672" stroke="#EEF0F3"/>
<line x1="1272.5" y1="-48" x2="1272.5" y2="672" stroke="#EEF0F3"/>
<line x1="1296.5" y1="-48" x2="1296.5" y2="672" stroke="#EEF0F3"/>
<line x1="1320.5" y1="-48" x2="1320.5" y2="672" stroke="#EEF0F3"/>
<line x1="1344.5" y1="-48" x2="1344.5" y2="672" stroke="#EEF0F3"/>
<line x1="1368.5" y1="-48" x2="1368.5" y2="672" stroke="#EEF0F3"/>
<line x1="1392.5" y1="-48" x2="1392.5" y2="672" stroke="#EEF0F3"/>
<line x1="1416.5" y1="-48" x2="1416.5" y2="672" stroke="#EEF0F3"/>
</g>
<rect width="1440" height="992" fill="url(#paint0_radial_628_19126)"/>
</g>
<defs>
<radialGradient id="paint0_radial_628_19126" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(720 496) rotate(90) scale(496 720)">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</radialGradient>
<clipPath id="clip0_628_19126">
<rect width="1440" height="992" fill="white"/>
</clipPath>
</defs>
</svg>
