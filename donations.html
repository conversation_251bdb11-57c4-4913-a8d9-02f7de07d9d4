<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Donations - BasicSwapDEX.com</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="css/tailwind.min.css">
    <link rel=icon sizes="32x32" type="image/png" href="images/favicon-32.png">
    <meta property="og:url" content="https://basicswapdex.com/donations">
    <meta property="og:site_name" content="BasicSwapDEX">
    <meta property="og:title" content="Support BasicSwap Development">
    <meta property="og:description" content="Help keep BasicSwap free and open-source. Your donations directly fund development, security audits, and community growth.">
    <meta property="og:image" content="/site-meta.png">
    <script src="js/main.js"></script>
    <style>
      .copy-feedback-popup {
        pointer-events: none;
        z-index: 9999;
      }
    </style>
  </head>
  <body class="antialiased bg-body text-body font-body">
    <!-- Navigation -->
    <section>
      <nav class="relative bg-gray-700">
        <div class="p-8 container flex flex-wrap items-center justify-between items-center mx-auto">
          <div class="flex justify-between items-center xl:w-full">
            <div class="xl:w-1/3">
              <a class="block max-w-max xl:mr-14" href="/">
                <img class="h-12" src="images/basicswap-logo.svg" alt="">
              </a>
            </div>
            <div class="hidden xl:block">
              <ul class="flex justify-center">
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="/">Home</a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="faq">FAQ</a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" href="about">About</a>
                </li>
                <li class="mr-12">
                  <a class="text-red-400 hover:text-red-300 font-medium inline-flex items-center" href="donations">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    </svg>
                    Donations
                  </a>
                </li>
                <li class="mr-12">
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" target=”_blank” href="https://docs.basicswapdex.com/docs/intro">Docs</a>
                </li>
                <li>
                  <a class="text-coolGray-400 hover:text-coolGray-50 font-medium" target="_blank" href="https://particl.news/tag/basicswap">Blog</a>
                </li>
              </ul>
            </div>
            <div class="hidden xl:block xl:w-1/3">
              <div class="flex items-center justify-end space-x-3">
                  <!-- Twitter Icon -->
                  <a class="inline-block p-2 text-coolGray-400 hover:text-white transition duration-200" href="https://x.com/BasicSwapDEX" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter">
                    <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </a>

                  <!-- Matrix Icon -->
                  <a class="inline-block p-2 text-coolGray-400 hover:text-white transition duration-200" href="https://matrix.to/#/#basicswap:matrix.org" target="_blank" rel="noopener noreferrer" title="Join us on Matrix">
                    <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M.632.55v22.9H2.28V24H0V0h2.28v.55zm7.043 7.26v1.157h.033c.309-.443.683-.784 1.117-1.024.433-.245.936-.365 1.507-.365.54 0 1.033.107 1.481.314.448.208.785.582 1.02 1.108.254-.374.6-.706 1.034-.992.434-.287.95-.43 1.546-.43.453 0 .872.056 1.26.167.388.11.716.286.993.53.276.245.489.559.646.951.152.392.23.863.23 1.417v5.728h-2.349V11.52c0-.286-.01-.559-.032-.812a1.755 1.755 0 0 0-.18-.66 1.106 1.106 0 0 0-.438-.448c-.194-.11-.457-.166-.785-.166-.332 0-.6.064-.803.189a1.38 1.38 0 0 0-.48.499 1.946 1.946 0 0 0-.231.696 5.56 5.56 0 0 0-.06.785v4.826h-2.35v-4.206c0-.265-.023-.52-.067-.766a1.589 1.589 0 0 0-.266-.652 1.32 1.32 0 0 0-.531-.46c-.219-.116-.506-.174-.863-.174-.22 0-.440.043-.659.132-.219.088-.417.207-.594.357-.177.149-.318.33-.423.542-.104.212-.157.452-.157.72v4.507H7.675V7.81zm15.693 16.64V.55h-1.648V0H24v24h-2.28v-.55z"/>
                    </svg>
                  </a>

                  <!-- GitHub Button -->
                <a class="flex flex-wrap justify-center inline-block px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-bold text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none" href="https://github.com/basicswap/" target="_blank" rel="noopener noreferrer">
                  <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" class="mr-2" viewBox="0 0 24 24"><g fill="#ffffff"><path fill-rule="evenodd" clip-rule="evenodd" fill="#ffffff" d="M12,0.3c-6.6,0-12,5.4-12,12c0,5.3,3.4,9.8,8.2,11.4 C8.8,23.8,9,23.4,9,23.1c0-0.3,0-1,0-2c-3.3,0.7-4-1.6-4-1.6c-0.5-1.4-1.3-1.8-1.3-1.8C2.5,17,3.7,17,3.7,17 c1.2,0.1,1.8,1.2,1.8,1.2c1.1,1.8,2.8,1.3,3.5,1c0.1-0.8,0.4-1.3,0.8-1.6c-2.7-0.3-5.5-1.3-5.5-5.9c0-1.3,0.5-2.4,1.2-3.2 C5.5,8.1,5,6.9,5.7,5.3c0,0,1-0.3,3.3,1.2c1-0.3,2-0.4,3-0.4c1,0,2,0.1,3,0.4c2.3-1.6,3.3-1.2,3.3-1.2c0.7,1.7,0.2,2.9,0.1,3.2 c0.8,0.8,1.2,1.9,1.2,3.2c0,4.6-2.8,5.6-5.5,5.9c0.4,0.4,0.8,1.1,0.8,2.2c0,1.6,0,2.9,0,3.3c0,0.3,0.2,0.7,0.8,0.6 c4.8-1.6,8.2-6.1,8.2-11.4C24,5.7,18.6,0.3,12,0.3z"></path></g></svg>
                  <span>BasicSwap Github</span>
                </a>
              </div>
            </div>
          </div>
          <button class="navbar-burger self-center xl:hidden">
            <svg width="35" height="35" viewbox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect class="text-coolGray-800" width="32" height="32" rx="6" fill="currentColor"></rect>
              <path class="text-coolGray-400" d="M7 12H25C25.2652 12 25.5196 11.8946 25.7071 11.7071C25.8946 11.5196 26 11.2652 26 11C26 10.7348 25.8946 10.4804 25.7071 10.2929C25.5196 10.1054 25.2652 10 25 10H7C6.73478 10 6.48043 10.1054 6.29289 10.2929C6.10536 10.4804 6 10.7348 6 11C6 11.2652 6.10536 11.5196 6.29289 11.7071C6.48043 11.8946 6.73478 12 7 12ZM25 15H7C6.73478 15 6.48043 15.1054 6.29289 15.2929C6.10536 15.4804 6 15.7348 6 16C6 16.2652 6.10536 16.5196 6.29289 16.7071C6.48043 16.8946 6.73478 17 7 17H25C25.2652 17 25.5196 16.8946 25.7071 16.7071C25.8946 16.5196 26 16.2652 26 16C26 15.7348 25.8946 15.4804 25.7071 15.2929C25.5196 15.1054 25.2652 15 25 15ZM25 20H7C6.73478 20 6.48043 20.1054 6.29289 20.2929C6.10536 20.4804 6 20.7348 6 21C6 21.2652 6.10536 21.5196 6.29289 21.7071C6.48043 21.8946 6.73478 22 7 22H25C25.2652 22 25.5196 21.8946 25.7071 21.7071C25.8946 21.5196 26 21.2652 26 21C26 20.7348 25.8946 20.4804 25.7071 20.2929C25.5196 20.1054 25.2652 20 25 20Z" fill="currentColor"></path>
            </svg>
          </button>
        </div>
      </nav>

      <!-- Mobile Menu -->
      <div class="navbar-menu hidden fixed top-0 left-0 z-50 w-full h-full bg-coolGray-900 bg-opacity-50">
        <div class="fixed top-0 left-0 bottom-0 w-full w-4/6 max-w-xs bg-coolGray-900">
          <nav class="relative p-6 h-full overflow-y-auto">
            <div class="flex flex-col justify-between h-full">
              <a class="inline-block" href="#">
                <img class="h-8" src="images/basicswap-logo.svg" alt="">
              </a>
              <ul class="py-6">
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="/">Home</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="faq">FAQ</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="about">About</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-white hover:text-white font-medium bg-coolGray-800 rounded-md" href="donations">Donations</a>
                </li>
                <li>
                  <a class="block py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" target="_blank" href="https://particl.news/tag/basicswap">Blog</a>
                </li>
                <li>
                  <a class="flex items-center py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="https://x.com/BasicSwapDEX" target="_blank" rel="noopener noreferrer">
                    <svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24" fill="currentColor" class="mr-3">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                    Twitter
                  </a>
                </li>
                <li>
                  <a class="flex items-center py-3 px-4 text-coolGray-400 hover:text-white font-medium hover:bg-coolGray-800 rounded-md" href="https://matrix.to/#/#basicswap:matrix.org" target="_blank" rel="noopener noreferrer">
                    <svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24" fill="currentColor" class="mr-3">
                      <path d="M.632.55v22.9H2.28V24H0V0h2.28v.55zm7.043 7.26v1.157h.033c.309-.443.683-.784 1.117-1.024.433-.245.936-.365 1.507-.365.54 0 1.033.107 1.481.314.448.208.785.582 1.02 1.108.254-.374.6-.706 1.034-.992.434-.287.95-.43 1.546-.43.453 0 .872.056 1.26.167.388.11.716.286.993.53.276.245.489.559.646.951.152.392.23.863.23 1.417v5.728h-2.349V11.52c0-.286-.01-.559-.032-.812a1.755 1.755 0 0 0-.18-.66 1.106 1.106 0 0 0-.438-.448c-.194-.11-.457-.166-.785-.166-.332 0-.6.064-.803.189a1.38 1.38 0 0 0-.48.499 1.946 1.946 0 0 0-.231.696 5.56 5.56 0 0 0-.06.785v4.826h-2.35v-4.206c0-.265-.023-.52-.067-.766a1.589 1.589 0 0 0-.266-.652 1.32 1.32 0 0 0-.531-.46c-.219-.116-.506-.174-.863-.174-.22 0-.440.043-.659.132-.219.088-.417.207-.594.357-.177.149-.318.33-.423.542-.104.212-.157.452-.157.72v4.507H7.675V7.81zm15.693 16.64V.55h-1.648V0H24v24h-2.28v-.55z"/>
                    </svg>
                    Matrix
                  </a>
                </li>
              </ul>
              <div class="flex flex-wrap">
                <div class="w-full">
                  <a class="inline-block py-4 px-5 w-full text-sm leading-5 text-white bg-blue-500 hover:bg-blue-600 font-medium text-center focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="https://github.com/basicswap/basicswap" target="_blank" rel="noopener noreferrer">BasicSwap Github</a>
                </div>
              </div>
            </div>
          </nav>
          <a class="navbar-close absolute top-5 p-4 right-3" href="#">
            <svg width="12" height="12" viewbox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.94004 5.99988L11.14 1.80655C11.2656 1.68101 11.3361 1.51075 11.3361 1.33321C11.3361 1.15568 11.2656 0.985415 11.14 0.859879C11.0145 0.734344 10.8442 0.663818 10.6667 0.663818C10.4892 0.663818 10.3189 0.734344 10.1934 0.859879L6.00004 5.05988L1.80671 0.859879C1.68117 0.734344 1.51091 0.663819 1.33337 0.663819C1.15584 0.663819 0.985576 0.734344 0.860041 0.859879C0.734505 0.985415 0.66398 1.15568 0.66398 1.33321C0.66398 1.51075 0.734505 1.68101 0.860041 1.80655L5.06004 5.99988L0.860041 10.1932C0.797555 10.2552 0.747959 10.3289 0.714113 10.4102C0.680267 10.4914 0.662842 10.5785 0.662842 10.6665C0.662842 10.7546 0.680267 10.8417 0.714113 10.9229C0.747959 11.0042 0.797555 11.0779 0.860041 11.1399C0.922016 11.2024 0.99575 11.252 1.07699 11.2858C1.15823 11.3197 1.24537 11.3371 1.33337 11.3371C1.42138 11.3371 1.50852 11.3197 1.58976 11.2858C1.671 11.252 1.74473 11.2024 1.80671 11.1399L6.00004 6.93988L10.1934 11.1399C10.2554 11.2024 10.3291 11.252 10.4103 11.2858C10.4916 11.3197 10.5787 11.3371 10.6667 11.3371C10.7547 11.3371 10.8419 11.3197 10.9231 11.2858C11.0043 11.252 11.0781 11.2024 11.14 11.1399C11.2025 11.0779 11.2521 11.0042 11.286 10.9229C11.3198 10.8417 11.3372 10.7546 11.3372 10.6665C11.3372 10.5785 11.3198 10.4914 11.286 10.4102C11.2521 10.3289 11.2025 10.2552 11.14 10.1932L6.94004 5.99988Z" fill="#8896AB"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- Hero Section -->
    <section class="relative bg-gray-700">
      <div class="container mx-auto overflow-hidden">
        <div class="relative z-10 overflow-hidden pt-16 pb-16">
          <div class="container px-4 mx-auto">
            <div class="text-center">
              <h1 class="mb-4 text-4xl md:text-5xl xl:text-6xl font-bold text-white font-heading tracking-px-n leading-none">Support BasicSwap</h1>
              <p class="mb-8 text-xl md:text-2xl text-white font-bold">Help Keep BasicSwap Free and Open-Source</p>
              <p class="mb-8 text-lg md:text-xl text-white font-medium">Your donations directly fund development, security audits, and community growth.</p>
              <p class="mb-8 text-lg md:text-xl text-white font-medium">Join the <a class="text-blue-500" href="https://matrix.to/#/#basicswap:matrix.org" target="_blank" rel="noopener noreferrer">Matrix channel</a> and come say hello!</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Support Matters Section -->
    <section class="py-24 md:py-32 bg-white overflow-hidden">
      <div class="container px-4 mx-auto">
        <div class="text-center mb-12">
          <h3 class="mb-8 text-3xl md:text-4xl leading-tight font-bold tracking-tighter">Why Your Support Matters</h3>
          <div class="flex justify-center mb-8">
            <svg class="w-16 h-16 text-red-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </div>
          <div class="max-w-4xl mx-auto">
            <p class="mb-6 text-lg md:text-xl text-coolGray-500 font-medium">
              BasicSwap is completely free and open-source software that charges no fees for its use. The project is entirely funded by generous community donations from users who believe in decentralized, censorship-resistant trading.
            </p>
            <p class="mb-8 text-lg md:text-xl text-coolGray-500 font-medium">
              Your donations are vital to keeping this project alive, accelerating development, and expanding our reach to more users who value financial freedom and privacy.
            </p>
          </div>
        </div>

        <!-- Benefits Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div class="p-8 text-center hover:bg-white rounded-md hover:shadow-xl transition duration-200">
            <div class="inline-flex h-16 w-16 mb-6 mx-auto items-center justify-center text-white bg-blue-500 rounded-lg">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h4 class="mb-4 text-xl md:text-2xl leading-tight font-bold">Core Development</h4>
            <p class="text-coolGray-500 font-medium">New features and improvements to the BasicSwap platform</p>
          </div>

          <div class="p-8 text-center hover:bg-white rounded-md hover:shadow-xl transition duration-200">
            <div class="inline-flex h-16 w-16 mb-6 mx-auto items-center justify-center text-white bg-blue-500 rounded-lg">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h4 class="mb-4 text-xl md:text-2xl leading-tight font-bold">Security Audits</h4>
            <p class="text-coolGray-500 font-medium">Professional testing and security infrastructure maintenance</p>
          </div>

          <div class="p-8 text-center hover:bg-white rounded-md hover:shadow-xl transition duration-200">
            <div class="inline-flex h-16 w-16 mb-6 mx-auto items-center justify-center text-white bg-blue-500 rounded-lg">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h4 class="mb-4 text-xl md:text-2xl leading-tight font-bold">Documentation</h4>
            <p class="text-coolGray-500 font-medium">Educational resources and comprehensive user guides</p>
          </div>

          <div class="p-8 text-center hover:bg-white rounded-md hover:shadow-xl transition duration-200">
            <div class="inline-flex h-16 w-16 mb-6 mx-auto items-center justify-center text-white bg-blue-500 rounded-lg">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h4 class="mb-4 text-xl md:text-2xl leading-tight font-bold">Community Growth</h4>
            <p class="text-coolGray-500 font-medium">Outreach and adoption initiatives worldwide</p>
          </div>
        </div>

        <div class="text-center">
          <p class="text-lg md:text-xl text-coolGray-500 font-medium">
            Together, we're building financial tools that empower individuals and resist censorship.<br> Thank you for being part of this movement toward true financial freedom.
          </p>
        </div>
      </div>
    </section>

    <!-- Donation Addresses Section -->
    <section class="py-24 bg-gray-700">
      <div class="container px-4 mx-auto">
        <div class="text-center mb-8">
          <h3 class="mb-4 text-3xl md:text-4xl leading-tight text-white font-bold tracking-tighter">Donation Addresses</h3>
          <p class="mb-8 text-lg md:text-xl text-white font-medium">
            Every contribution helps make decentralized trading more accessible to everyone.
          </p>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full mx-auto">
            <thead>
              <tr>
                <th class="text-left py-4 px-6 bg-gray-800 font-semibold text-gray-300 uppercase text-sm w-1/4">
                  Cryptocurrency
                </th>
                <th class="text-left py-4 px-6 bg-gray-800 font-semibold text-gray-300 uppercase text-sm w-3/4">
                  Donation Address
                </th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-b border-gray-600 hover:bg-gray-600 transition duration-200">
                <td class="py-4 px-6 bg-gray-700 w-1/4">
                  <div class="flex items-center">
                    <img class="h-8 w-8 mr-4" src="images/monero-circle.png" alt="Monero">
                    <span class="font-medium text-white">Monero (XMR)</span>
                  </div>
                </td>
                <td class="py-4 px-6 bg-gray-700 w-3/4">
                  <input type="text" readonly class="donation-address w-full p-3 bg-gray-700 border-2 border-blue-500 rounded-lg font-mono text-sm text-white cursor-pointer hover:border-blue-400 focus:border-blue-400 focus:outline-none transition duration-200" value="8BuQsYBNdfhfoWsvVR1unE7YuZEoTkC4hANaPm2fD6VR5VM2DzQoJhq2CHHXUN1UCWQfH3dctJgorSRxksVa5U4RNTJkcAc">
                </td>
              </tr>
              <tr class="border-b border-gray-600 hover:bg-gray-600 transition duration-200">
                <td class="py-4 px-6 bg-gray-700 w-1/4">
                  <div class="flex items-center">
                    <img class="h-8 w-8 mr-4" src="images/litecoin-circle.png" alt="Litecoin">
                    <span class="font-medium text-white">Litecoin (LTC)</span>
                  </div>
                </td>
                <td class="py-4 px-6 bg-gray-700 w-3/4">
                  <input type="text" readonly class="donation-address w-full p-3 bg-gray-700 border-2 border-blue-500 rounded-lg font-mono text-sm text-white cursor-pointer hover:border-blue-400 focus:border-blue-400 focus:outline-none transition duration-200" value="ltc1qevlumv48nz2afl0re9ml4tdewc56svxq3egkyt">
                </td>
              </tr>
              <tr class="border-b border-gray-600 hover:bg-gray-600 transition duration-200">
                <td class="py-4 px-6 bg-gray-700 w-1/4">
                  <div class="flex items-center">
                    <img class="h-8 w-8 mr-4" src="images/litecoin-circle.png" alt="Litecoin MWEB">
                    <span class="font-medium text-white">Litecoin (LTC) MWEB</span>
                  </div>
                </td>
                <td class="py-4 px-6 bg-gray-700 w-3/4">
                  <input type="text" readonly class="donation-address w-full p-3 bg-gray-700 border-2 border-blue-500 rounded-lg font-mono text-sm text-white cursor-pointer hover:border-blue-400 focus:border-blue-400 focus:outline-none transition duration-200" value="ltcmweb1qqt9rwznnxzkghv4s5wgtwxs0m0ry6n3atp95f47slppapxljde3xyqmdlnrc8ag7y2k354jzdc4pc4ks0kr43jehr77lngdecgh6689nn5mgv5yn">
                </td>
              </tr>
              <tr class="border-b border-gray-600 hover:bg-gray-600 transition duration-200">
                <td class="py-4 px-6 bg-gray-700 w-1/4">
                  <div class="flex items-center">
                    <img class="h-8 w-8 mr-4" src="images/bitcoin-circle.png" alt="Bitcoin">
                    <span class="font-medium text-white">Bitcoin (BTC)</span>
                  </div>
                </td>
                <td class="py-4 px-6 bg-gray-700 w-3/4">
                  <input type="text" readonly class="donation-address w-full p-3 bg-gray-700 border-2 border-blue-500 rounded-lg font-mono text-sm text-white cursor-pointer hover:border-blue-400 focus:border-blue-400 focus:outline-none transition duration-200" value="******************************************">
                </td>
              </tr>
              <tr class="hover:bg-gray-600 transition duration-200">
                <td class="py-4 px-6 bg-gray-700 w-1/4">
                  <div class="flex items-center">
                    <img class="h-8 w-8 mr-4" src="images/particl-circle.png" alt="Particl">
                    <span class="font-medium text-white">Particl (PART)</span>
                  </div>
                </td>
                <td class="py-4 px-6 bg-gray-700 w-3/4">
                  <input type="text" readonly class="donation-address w-full p-3 bg-gray-700 border-2 border-blue-500 rounded-lg font-mono text-sm text-white cursor-pointer hover:border-blue-400 focus:border-blue-400 focus:outline-none transition duration-200" value="pw1qf59ef0zjdckldjs8smfhv4j04gsjv302w7pdpz">
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-24 bg-white">
      <div class="container px-4 mx-auto">
        <div class="relative py-16 md:py-32 px-6 text-center bg-gray-700 overflow-hidden rounded-7xl">
          <div class="relative z-10 mx-auto md:max-w-2xl">
            <h3 class="mb-4 text-3xl md:text-4xl leading-tight text-white font-bold tracking-tighter">Want to Learn More?</h3>
            <p class="mb-8 text-lg md:text-xl text-white font-medium">Learn more about the underlying technology that powers the world's most decentralized DEX and bring your financial sovereignty to the next level.</p>

            <div class="flex flex-wrap justify-center">
              <div class="w-full md:w-auto p-1.5 ml-2">
                <a class="w-full inline-block py-3 px-4 text-sm leading-5 text-blue-50 bg-blue-500 hover:bg-blue-600 font-bold focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="https://academy.particl.io/en/latest/basicswap-dex/basicswap_explained.html" target="_blank" rel="noopener noreferrer">How-to Guides</a>
              </div>
              <div class="w-full md:w-auto p-1.5 ml-2">
                <a class="w-full inline-block py-3 px-4 text-sm leading-5 text-blue-50 bg-blue-500 hover:bg-blue-600 font-bold focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="/about">Under the Hood</a>
              </div>
            </div>
          </div>

          <div class="relative z-10 mx-auto md:max-w-2xl mt-20">
            <h3 class="mb-4 text-3xl md:text-2xl leading-tight text-white font-bold tracking-tighter">Looking for an asset not listed on BasicSwap?</h3>
            <p class="mb-8 text-lg md:text-xl text-white font-medium">BasicSwap is completely open-source, meaning that anyone can contribute by integrating assets. If you'd like to integrate a coin yourself, go to Github to find the relevant documentation to get you started!</p>

            <div class="flex flex-wrap justify-center">
              <div class="w-full md:w-auto p-1.5 ml-2">
                <a class="w-full inline-block py-3 px-4 text-sm leading-5 text-blue-50 bg-blue-500 hover:bg-blue-600 font-bold focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50 rounded-md" href="https://academy.particl.io/en/latest/basicswap-guides/basicswapguides_apply.html#manual-integration" target="_blank" rel="noopener noreferrer">Add it Yourself</a>
              </div>
            </div>
          </div>

          <img class="absolute top-0 left-0 w-28 md:w-auto" src="images/wave2-yellow.svg" alt="">
          <img class="absolute right-6 top-6 w-28 md:w-auto" src="images/dots3-green.svg" alt="">
          <img class="absolute right-0 bottom-0 w-28 md:w-auto" src="images/wave3-red.svg" alt="">
          <img class="absolute left-6 bottom-6 w-28 md:w-auto" src="images/dots3-violet.svg" alt="">
        </div>
      </div>
    </section>

    <!-- Footer -->
    <section class="bg-white overflow-hidden">
      <div class="container px-4 mx-auto">
        <div class="flex flex-wrap lg:items-center pt-24 pb-12 -mx-4">
          <div class="w-full md:w-1/4 lg:w-auto px-4">
            <a class="block mb-5 md:mb-0 max-w-max" href="/">
              <img class="h-8" src="images/basicswap-logo-dark.svg" alt="">
            </a>
          </div>
          <div class="w-full md:w-3/4 lg:flex-1 px-4">
            <div class="flex flex-wrap justify-end -mx-3 lg:-mx-6">
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="/">Home</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="faq">FAQ</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="about">About</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" target=”_blank” href="https://docs.basicswapdex.com/docs/intro">Docs</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-red-500 hover:text-red-600 font-medium inline-flex items-center" href="donations">
                  <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                  </svg>
                  Donations
                </a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" target="_blank" href="https://particl.news/tag/basicswap">Blog</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="terms">Terms and Conditions</a>
              </div>
              <div class="w-full md:w-auto p-3 md:py-0 md:px-6">
                <a class="inline-block text-lg md:text-xl text-coolGray-500 hover:text-coolGray-600 font-medium" href="/mediakit/mediakit-basicswap.zip">Mediakit</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="border-b border-coolGray-100"></div>
      <div class="container px-4 mx-auto mb-10">
        <div class="flex flex-wrap items-center pt-12">
          <div class="w-full md:w-1/2 mb-6 md:mb-0">
            <div class="flex items-center">
              <p class="mr-1 text-sm text-gray-900 font-medium">© 2025~ </p>
              <p class="text-sm text-coolGray-400 font-medium">BasicSwapDEX</p>
            </div>
          </div>
          <div class="w-full md:w-1/2">
            <div class="flex flex-wrap md:justify-end -mx-5">
              <div class="px-5">
                <a class="inline-block text-coolGray-300 hover:text-coolGray-400" href="https://x.com/BasicSwapDEX" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
              </div>
              <div class="px-5">
                <a class="inline-block text-coolGray-300 hover:text-coolGray-400" href="https://matrix.to/#/#basicswap:matrix.org" target="_blank" rel="noopener noreferrer" title="Join us on Matrix">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M.632.55v22.9H2.28V24H0V0h2.28v.55zm7.043 7.26v1.157h.033c.309-.443.683-.784 1.117-1.024.433-.245.936-.365 1.507-.365.54 0 1.033.107 1.481.314.448.208.785.582 1.02 1.108.254-.374.6-.706 1.034-.992.434-.287.95-.43 1.546-.43.453 0 .872.056 1.26.167.388.11.716.286.993.53.276.245.489.559.646.951.152.392.23.863.23 1.417v5.728h-2.349V11.52c0-.286-.01-.559-.032-.812a1.755 1.755 0 0 0-.18-.66 1.106 1.106 0 0 0-.438-.448c-.194-.11-.457-.166-.785-.166-.332 0-.6.064-.803.189a1.38 1.38 0 0 0-.48.499 1.946 1.946 0 0 0-.231.696 5.56 5.56 0 0 0-.06.785v4.826h-2.35v-4.206c0-.265-.023-.52-.067-.766a1.589 1.589 0 0 0-.266-.652 1.32 1.32 0 0 0-.531-.46c-.219-.116-.506-.174-.863-.174-.22 0-.440.043-.659.132-.219.088-.417.207-.594.357-.177.149-.318.33-.423.542-.104.212-.157.452-.157.72v4.507H7.675V7.81zm15.693 16.64V.55h-1.648V0H24v24h-2.28v-.55z"/>
                  </svg>
                </a>
              </div>
              <div class="px-5">
                <a class="inline-block text-coolGray-300 hover:text-coolGray-400" href="https://github.com/basicswap/basicswap" target="_blank" rel="noopener noreferrer" title="View on GitHub">
                  <svg width="18" height="18" viewbox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 0C4.0275 0 0 4.13211 0 9.22838C0 13.3065 2.5785 16.7648 6.15375 17.9841C6.60375 18.0709 6.76875 17.7853 6.76875 17.5403C6.76875 17.3212 6.76125 16.7405 6.7575 15.9712C4.254 16.5277 3.726 14.7332 3.726 14.7332C3.3165 13.6681 2.72475 13.3832 2.72475 13.3832C1.9095 12.8111 2.78775 12.8229 2.78775 12.8229C3.6915 12.887 4.16625 13.7737 4.16625 13.7737C4.96875 15.1847 6.273 14.777 6.7875 14.5414C6.8685 13.9443 7.10025 13.5381 7.3575 13.3073C5.35875 13.0764 3.258 12.2829 3.258 8.74709C3.258 7.73988 3.60675 6.91659 4.18425 6.27095C4.083 6.03774 3.77925 5.0994 4.263 3.82846C4.263 3.82846 5.01675 3.58116 6.738 4.77462C7.458 4.56958 8.223 4.46785 8.988 4.46315C9.753 4.46785 10.518 4.56958 11.238 4.77462C12.948 3.58116 13.7017 3.82846 13.7017 3.82846C14.1855 5.0994 13.8818 6.03774 13.7917 6.27095C14.3655 6.91659 14.7142 7.73988 14.7142 8.74709C14.7142 12.2923 12.6105 13.0725 10.608 13.2995C10.923 13.5765 11.2155 14.1423 11.2155 15.0071C11.2155 16.242 11.2043 17.2344 11.2043 17.5341C11.2043 17.7759 11.3617 18.0647 11.823 17.9723C15.4237 16.7609 18 13.3002 18 9.22838C18 4.13211 13.9703 0 9 0Z" fill="currentColor"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for Copy Functionality -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        setupDonationAddressCopy();
        setupMobileMenu();
      });

      function setupDonationAddressCopy() {
        const donationAddresses = document.querySelectorAll('.donation-address');

        donationAddresses.forEach(element => {
          element.addEventListener('click', function(e) {
            const address = this.value;
            copyToClipboard(address);
            this.select();
            this.setSelectionRange(0, 99999);
            this.classList.add('bg-blue-50', 'border-blue-500');
            showCopyFeedback(this);

            setTimeout(() => {
              this.classList.remove('bg-blue-50', 'border-blue-500');
              this.blur();
            }, 1000);
          });
        });
      }

      function setupMobileMenu() {
        const burger = document.querySelector('.navbar-burger');
        const menu = document.querySelector('.navbar-menu');
        const close = document.querySelector('.navbar-close');

        if (burger) {
          burger.addEventListener('click', function() {
            menu.classList.remove('hidden');
          });
        }

        if (close) {
          close.addEventListener('click', function() {
            menu.classList.add('hidden');
          });
        }
      }

      let activeTooltip = null;

      function showCopyFeedback(element) {
        if (activeTooltip && activeTooltip.parentNode) {
          activeTooltip.parentNode.removeChild(activeTooltip);
        }

        const popup = document.createElement('div');
        popup.className = 'copy-feedback-popup fixed z-50 bg-blue-600 text-white text-sm py-2 px-3 rounded-md shadow-lg pointer-events-none';
        popup.innerText = 'Address copied!';
        document.body.appendChild(popup);

        activeTooltip = popup;
        updateTooltipPosition(popup, element);

        const scrollHandler = () => {
          if (popup.parentNode) {
            updateTooltipPosition(popup, element);
          }
        };

        window.addEventListener('scroll', scrollHandler, { passive: true });

        popup.style.opacity = '0';
        popup.style.transition = 'opacity 0.2s ease-in-out';

        setTimeout(() => {
          popup.style.opacity = '1';
        }, 10);

        setTimeout(() => {
          window.removeEventListener('scroll', scrollHandler);
          popup.style.opacity = '0';

          setTimeout(() => {
            if (popup.parentNode) {
              popup.parentNode.removeChild(popup);
            }
            if (activeTooltip === popup) {
              activeTooltip = null;
            }
          }, 200);
        }, 1500);
      }

      function updateTooltipPosition(tooltip, element) {
        const rect = element.getBoundingClientRect();
        let top = rect.top - tooltip.offsetHeight - 8;
        const left = rect.left + rect.width / 2;

        if (top < 10) {
          top = rect.bottom + 8;
        }

        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
        tooltip.style.transform = 'translateX(-50%)';
      }

      function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(text).catch(err => {
            console.error('Failed to copy: ', err);
            copyToClipboardFallback(text);
          });
        } else {
          copyToClipboardFallback(text);
        }
      }

      function copyToClipboardFallback(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand('copy');
        } catch (err) {
          console.error('Failed to copy text: ', err);
        }

        document.body.removeChild(textArea);
      }
    </script>
  </body>
</html>